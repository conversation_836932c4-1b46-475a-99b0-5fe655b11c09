"use client";

import {
  Card,
  CardBody,
  Input,
  Button,
  Tooltip,
  Select,
  SelectItem,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Form,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import React, { useState, useEffect, useCallback, useRef } from "react";

import { FieldProps } from "@/types/fields";

enum TaskState {
  NOT_COMPLETED,
  IN_PROGRESS,
  COMPLETED,
}

const Field = React.memo(function Field({
  id: _fieldId,
  title,
  type,
  observation,
  milestone,
  description,
  options = [],
  edit = false,
  subtasks = [],
  value: initialValue = "",
  onChange,
  cancelEditTrigger,
}: FieldProps & {
  onChange?: (id: number, value: string, observation?: string) => void;
  cancelEditTrigger?: boolean;
}) {
  const [value, setValue] = useState<string>(initialValue || "");
  const [obs, setObs] = useState<string>(observation);
  const [isObsEditing, setIsObsEditing] = useState<boolean>(false);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [tempObs, setTempObs] = useState<string>(observation);
  const [subtaskValues, setSubtaskValues] = useState<{ [key: string]: string }>(
    {},
  );

  // Debounce timer ref for text inputs
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [subtaskObservations, setSubtaskObservations] = useState<{
    [key: string]: string;
  }>({});
  const [isSubtaskObsEditing, setIsSubtaskObsEditing] = useState<{
    [key: string]: boolean;
  }>({});
  const [tempSubtaskObs, setTempSubtaskObs] = useState<{
    [key: string]: string;
  }>({});
  const [subtaskModalOpen, setSubtaskModalOpen] = useState<string | null>(null);

  // Removed console.log for performance
  // useEffect(() => {
  //   console.log("Options changed:", options);
  // }, [options]);

  useEffect(() => {
    if (subtasks.length > 0) {
      const initialObservations: { [key: string]: string } = {};
      const initialTempObs: { [key: string]: string } = {};
      const initialEditingState: { [key: string]: boolean } = {};
      const initialValues: { [key: string]: string } = {};

      subtasks.forEach((subtask) => {
        const subtaskKey = subtask.id.toString();

        initialObservations[subtaskKey] = subtask.observation || "";
        initialTempObs[subtaskKey] = subtask.observation || "";
        initialEditingState[subtaskKey] = false;
        initialValues[subtaskKey] = subtask.value || "";
      });

      setSubtaskObservations(initialObservations);
      setTempSubtaskObs(initialTempObs);
      setIsSubtaskObsEditing(initialEditingState);
      setSubtaskValues(initialValues);
    }
  }, [subtasks]);

  // Notify parent when value changes (for controlled components)
  useEffect(() => {
    if (onChange && value !== initialValue) {
      // Use debounced change for text inputs, immediate for others
      if (type === "INFORMATIVE") {
        // For text inputs, we'll let the debounced handler manage this
        return;
      } else {
        // For selects and other inputs, notify immediately
        onChange(_fieldId, value, obs);
      }
    }
  }, [value, onChange, _fieldId, obs, initialValue, type]);

  useEffect(() => {
    setValue(initialValue);
    setObs(observation);

    // Reset subtask observations and values to original values when cancel is triggered
    if (subtasks.length > 0) {
      const initialObservations: { [key: string]: string } = {};
      const initialValues: { [key: string]: string } = {};

      subtasks.forEach((subtask) => {
        initialObservations[subtask.id] = subtask.observation || "";
        initialValues[subtask.id.toString()] = subtask.value || "";
      });

      setSubtaskObservations(initialObservations);
      setTempSubtaskObs(initialObservations);
      setSubtaskValues(initialValues);
      setIsSubtaskObsEditing((prev) => {
        const reset: { [key: string]: boolean } = {};

        Object.keys(prev).forEach((key) => {
          reset[key] = false;
        });

        return reset;
      });
    }
  }, [cancelEditTrigger, observation, initialValue, subtasks]);

  const tareaOptions = [
    { key: "pendiente", label: "Pendiente", value: TaskState.NOT_COMPLETED },
    { key: "en_progreso", label: "En progreso", value: TaskState.IN_PROGRESS },
    { key: "completado", label: "Completado", value: TaskState.COMPLETED },
    { key: "no_aplica", label: "N/A", value: TaskState.COMPLETED },
  ];

  // Debounced onChange for text inputs to improve performance
  const debouncedOnChange = useCallback((newValue: string, currentObs: string) => {
    if (onChange) {
      onChange(_fieldId, newValue, currentObs);
    }
  }, [onChange, _fieldId]);

  // Immediate onChange for non-text inputs (selects, etc.)
  const handleImmediateChange = useCallback(
    (newValue: string) => {
      setValue(newValue);
      if (onChange) {
        onChange(_fieldId, newValue, obs);
      }
    },
    [onChange, _fieldId, obs],
  );

  // Debounced change handler for text inputs
  const handleDebouncedChange = useCallback(
    (newValue: string) => {
      setValue(newValue);

      // Clear existing timer
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      // Set new timer for debounced onChange call
      debounceTimerRef.current = setTimeout(() => {
        debouncedOnChange(newValue, obs);
      }, 300); // 300ms debounce delay
    },
    [debouncedOnChange, obs],
  );

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  const handleModalOpen = () => {
    setTempObs(obs);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  const handleSaveObservation = (e: React.FormEvent) => {
    e.preventDefault();
    setObs(tempObs);
    setIsModalOpen(false);
    // Notify parent component about observation change
    if (onChange) {
      onChange(_fieldId, value, tempObs);
    }
  };

  const toggleObsEditing = () => {
    if (isObsEditing) {
      setObs(tempObs);
    } else {
      setTempObs(obs);
    }
    setIsObsEditing(!isObsEditing);
  };

  const handleObsKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      toggleObsEditing();
    } else if (e.key === "Escape") {
      setTempObs(obs);
      setIsObsEditing(false);
    }
  };

  // Handle subtask value changes and update the main field value with JSON array
  const handleSubtaskValueChange = useCallback((subtaskId: string, newValue: string) => {
    // Removed console.log for performance
    setSubtaskValues((prev) => {
      const updatedSubtaskValues = {
        ...prev,
        [subtaskId]: newValue,
      };

      // Create an array of subtask values in the correct order (matching subtasks array)
      const subtaskValuesArray = subtasks.map(
        (subtask) => updatedSubtaskValues[subtask.id.toString()] || "",
      );

      // Convert to JSON string and update the main field value
      // This ensures the API receives the subtask values as a JSON array
      const jsonValue = JSON.stringify(subtaskValuesArray);

      // Update the main field value and notify parent immediately for subtasks
      setValue(jsonValue);
      if (onChange) {
        onChange(_fieldId, jsonValue, obs);
      }

      return updatedSubtaskValues;
    });
  }, [subtasks, setValue, onChange, _fieldId, obs]);

  const handleSubtaskObservationChange = (
    subtaskId: string,
    newObs: string,
  ) => {
    setSubtaskObservations((prev) => ({
      ...prev,
      [subtaskId]: newObs,
    }));
  };

  const toggleSubtaskObsEditing = (subtaskId: string) => {
    if (isSubtaskObsEditing[subtaskId]) {
      setSubtaskObservations((prev) => ({
        ...prev,
        [subtaskId]: tempSubtaskObs[subtaskId],
      }));
    } else {
      setTempSubtaskObs((prev) => ({
        ...prev,
        [subtaskId]: subtaskObservations[subtaskId] || "",
      }));
    }

    setIsSubtaskObsEditing((prev) => ({
      ...prev,
      [subtaskId]: !prev[subtaskId],
    }));
  };

  // Handle subtask observation keypresses like Enter and Escape
  const handleSubtaskObsKeyDown = (
    subtaskId: string,
    e: React.KeyboardEvent<HTMLInputElement>,
  ) => {
    if (e.key === "Enter") {
      toggleSubtaskObsEditing(subtaskId);
    } else if (e.key === "Escape") {
      setTempSubtaskObs((prev) => ({
        ...prev,
        [subtaskId]: subtaskObservations[subtaskId] || "",
      }));
      setIsSubtaskObsEditing((prev) => ({
        ...prev,
        [subtaskId]: false,
      }));
    }
  };

  const handleSubtaskModalOpen = (subtaskId: string) => {
    if (!subtaskObservations[subtaskId]) {
      // If there's no existing observation, set up a new one
      setTempSubtaskObs((prev) => ({
        ...prev,
        [subtaskId]: "",
      }));
    } else {
      // If there's an existing observation, use it
      setTempSubtaskObs((prev) => ({
        ...prev,
        [subtaskId]: subtaskObservations[subtaskId] || "",
      }));
    }
    setSubtaskModalOpen(subtaskId);
  };

  const handleSubtaskModalClose = () => {
    setSubtaskModalOpen(null);
  };

  const handleSaveSubtaskObservation = (
    subtaskId: string,
    e: React.FormEvent,
  ) => {
    e.preventDefault();
    setSubtaskObservations((prev) => ({
      ...prev,
      [subtaskId]: tempSubtaskObs[subtaskId] || "",
    }));
    setSubtaskModalOpen(null);
  };

  const renderFieldInput = () => {
    const hasObs = obs || isObsEditing;
    const inputClassName = hasObs ? "w-1/2 mx-4" : "w-full mx-4";

    switch (type) {
      case "INFORMATIVE":
        return (
          <Input
            className={inputClassName}
            isDisabled={!edit}
            placeholder="Insertar respuesta"
            value={value}
            onValueChange={handleDebouncedChange}
          />
        );
      case "TASK":
        return (
          <Select
            className={inputClassName}
            isDisabled={!edit}
            placeholder="Seleccionar estado tarea"
            selectedKeys={value ? [value] : []}
            onChange={(e) => handleImmediateChange(e.target.value)}
          >
            {tareaOptions.map((option) => (
              <SelectItem key={option.key}>{option.label}</SelectItem>
            ))}
          </Select>
        );
      case "DOCUMENT":
        return (
          <Select
            className={inputClassName}
            isDisabled={!edit}
            placeholder="Seleccionar estado documento"
            selectedKeys={value ? [value] : []}
            onChange={(e) => handleImmediateChange(e.target.value)}
          >
            {tareaOptions.map((option) => (
              <SelectItem key={option.key}>{option.label}</SelectItem>
            ))}
          </Select>
        );
      case "TASK_WITH_SUBTASKS":
        if (subtasks && subtasks.length > 0) {
          return null;
        }

        return (
          <Select
            className={inputClassName}
            isDisabled={!edit}
            placeholder="Seleccionar estado subtarea"
            selectedKeys={value ? [value] : []}
            onChange={(e) => setValue(e.target.value)}
          >
            {tareaOptions.map((option) => (
              <SelectItem key={option.key}>{option.label}</SelectItem>
            ))}
          </Select>
        );
      case "SELECTION":
        return (
          <Select
            className={inputClassName}
            isDisabled={!edit}
            placeholder={"Seleccionar opción selecc"}
            selectedKeys={value ? [value] : []}
            onChange={(e) => handleImmediateChange(e.target.value)}
          >
            {options.map((option: any) => (
              <SelectItem key={option.key}>{option.label}</SelectItem>
            ))}
          </Select>
        );
      default:
        return (
          <Input
            className={inputClassName}
            isDisabled={!edit}
            placeholder="Default"
            value={value}
            onValueChange={handleDebouncedChange}
          />
        );
    }
  };

  const dotColor = () => {
    switch (type) {
      case "INFORMATIVE":
        if (value) return "bg-green-500";
        else return "bg-red-500";
      case "TASK":
      case "DOCUMENT":
        const selectedOption = tareaOptions.find(
          (option) => option.key === value,
        );

        if (!selectedOption) return "bg-red-500";
        switch (selectedOption.value) {
          case TaskState.COMPLETED:
            return "bg-green-500";
          case TaskState.IN_PROGRESS:
            return "bg-yellow-500";
          case TaskState.NOT_COMPLETED:
          default:
            return "bg-red-500";
        }
      case "SELECTION":
        const selectedSelectionOption = options.find(
          (option) => option.key === value,
        );

        // Removed console.log for performance

        if (!selectedSelectionOption) return "bg-red-500";
        if (selectedSelectionOption.countAsCompleted === false)
          return "bg-yellow-500";
        else return "bg-green-500";
      case "TASK_WITH_SUBTASKS":
        if (subtasks.length > 0) {
          const allSubtasksCompleted = subtasks.every((subtask) => {
            const subtaskValue = subtaskValues[subtask.id.toString()];

            return (
              subtaskValue &&
              tareaOptions.find((option) => option.key === subtaskValue)
                ?.value === TaskState.COMPLETED
            );
          });

          const anySubtaskInProgress = subtasks.some((subtask) => {
            const subtaskValue = subtaskValues[subtask.id.toString()];

            return (
              (subtaskValue &&
                tareaOptions.find((option) => option.key === subtaskValue)
                  ?.value === TaskState.IN_PROGRESS) ||
              tareaOptions.find((option) => option.key === subtaskValue)
                ?.value === TaskState.COMPLETED
            );
          });

          return allSubtasksCompleted
            ? "bg-green-500"
            : anySubtaskInProgress
              ? "bg-yellow-500"
              : "bg-red-500";
        }

        return "bg-gray-500";
      default:
        return "bg-gray-500";
    }
  };

  const getSubtaskDotColor = (subtaskId: string) => {
    const subtaskValue = subtaskValues[subtaskId];

    if (!subtaskValue) return "bg-red-500";

    const selectedOption = tareaOptions.find(
      (option) => option.key === subtaskValue,
    );

    if (!selectedOption) return "bg-red-500";
    switch (selectedOption.value) {
      case TaskState.COMPLETED:
        return "bg-green-500";
      case TaskState.IN_PROGRESS:
        return "bg-yellow-500";
      case TaskState.NOT_COMPLETED:
      default:
        return "bg-red-500";
    }
  };

  const subtasksItems = () => {
    if (subtasks.length === 0) return null;

    return subtasks.map((subtask) => (
      <Card key={subtask.id} className="mb-4 ml-8">
        <CardBody>
          <div className="relative flex justify-between items-center w-full">
            <div
              className={`absolute top-0 right-0 -mt-2 -mr-2 w-3 h-3 rounded-full ${getSubtaskDotColor(
                subtask.id.toString(),
              )} border-2 border-white`}
            />

            <div className="flex flex-col gap-1 max-w-80">
              <div className="flex items-center gap-2">
                <Icon
                  className="flex-shrink-0 text-default-500"
                  icon="lucide:list-todo"
                />
                <span className="font-medium truncate">{subtask.title}</span>
                <Tooltip
                  classNames={{
                    content:
                      "py-2 px-3 shadow-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700",
                  }}
                  content={
                    <div className="p-2">
                      <p className="font-bold">{subtask.title}</p>
                      <p className="text-small">{subtask.description}</p>
                    </div>
                  }
                  placement="top"
                  showArrow={true}
                >
                  <Icon
                    className="text-default-400 flex-shrink-0 cursor-help"
                    icon="lucide:info"
                  />
                </Tooltip>
              </div>
              <span className="text-small text-default-500 truncate">
                Subtarea
              </span>
            </div>

            <Select
              className={
                subtaskObservations[subtask.id.toString()]
                  ? "w-1/2 mx-4"
                  : "w-full mx-4"
              }
              isDisabled={!edit}
              placeholder="Seleccionar estado"
              selectedKeys={
                subtaskValues[subtask.id.toString()]
                  ? [subtaskValues[subtask.id.toString()]]
                  : []
              }
              onChange={(e) =>
                handleSubtaskValueChange(subtask.id.toString(), e.target.value)
              }
            >
              {tareaOptions.map((option) => (
                <SelectItem key={option.key}>{option.label}</SelectItem>
              ))}
            </Select>

            {subtaskObservations[subtask.id.toString()] ? (
              <div className="flex items-center w-1/2 mx-4">
                <Input
                  className="flex-grow"
                  endContent={
                    isSubtaskObsEditing[subtask.id] ? (
                      <div className="flex items-center gap-2">
                        <Button
                          isIconOnly
                          aria-label="Cancel"
                          isDisabled={!edit}
                          size="sm"
                          variant="light"
                          onPress={() => {
                            setTempSubtaskObs((prev) => ({
                              ...prev,
                              [subtask.id.toString()]:
                                subtaskObservations[subtask.id.toString()] ||
                                "",
                            }));
                            setIsSubtaskObsEditing((prev) => ({
                              ...prev,
                              [subtask.id.toString()]: false,
                            }));
                          }}
                        >
                          <Icon className="text-default-500" icon="lucide:x" />
                        </Button>
                        <Button
                          isIconOnly
                          aria-label="Save"
                          color="primary"
                          isDisabled={!edit}
                          size="sm"
                          variant="flat"
                          onPress={() =>
                            toggleSubtaskObsEditing(subtask.id.toString())
                          }
                        >
                          <Icon icon="lucide:check" />
                        </Button>
                      </div>
                    ) : (
                      <Button
                        isIconOnly
                        aria-label="Edit inline"
                        className="flex-shrink-0"
                        color="primary"
                        isDisabled={!edit}
                        size="sm"
                        variant="flat"
                        onPress={() =>
                          toggleSubtaskObsEditing(subtask.id.toString())
                        }
                      >
                        <Icon
                          className="text-default-500"
                          icon="lucide:pencil"
                        />
                      </Button>
                    )
                  }
                  isDisabled={!edit}
                  placeholder="Observación"
                  readOnly={
                    !isSubtaskObsEditing[subtask.id.toString()] || !edit
                  }
                  startContent={
                    <Icon
                      className="text-default-400 flex-shrink-0"
                      icon="lucide:message-square"
                    />
                  }
                  value={
                    isSubtaskObsEditing[subtask.id.toString()]
                      ? tempSubtaskObs[subtask.id.toString()]
                      : subtaskObservations[subtask.id.toString()]
                  }
                  onKeyDown={
                    isSubtaskObsEditing[subtask.id.toString()] && edit
                      ? (e) => handleSubtaskObsKeyDown(subtask.id.toString(), e)
                      : undefined
                  }
                  onValueChange={(val) =>
                    setTempSubtaskObs((prev) => ({
                      ...prev,
                      [subtask.id.toString()]: val,
                    }))
                  }
                />
              </div>
            ) : null}

            <div className="flex-shrink-0">
              <Button
                isIconOnly
                aria-label="View subtask details"
                className="flex-shrink-0"
                color="primary"
                size="sm"
                variant="flat"
                onPress={() => handleSubtaskModalOpen(subtask.id.toString())}
              >
                <Icon className="text-lg" icon={"lucide:eye"} />
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>
    ));
  };

  return (
    <>
      <Card className="mb-4">
        <CardBody>
          <div className="relative flex justify-between items-center w-full">
            <div
              className={`absolute top-0 right-0 -mt-2 -mr-2 w-3 h-3 rounded-full ${dotColor()} border-2 border-white`}
            />
            <div className="flex flex-col gap-1 min-w-80 max-w-80">
              <div className="flex items-center gap-2">
                <Icon
                  className={
                    !milestone
                      ? "flex-shrink-0 text-white"
                      : "flex-shrink-0 text-warning-500"
                  }
                  icon="lucide:milestone"
                />
                <span className={"font-medium truncate"}>{title}</span>
                <Tooltip
                  classNames={{
                    content:
                      "py-2 px-3 shadow-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700",
                  }}
                  content={
                    <div className="p-2">
                      <p className="font-bold">{title}</p>
                      <p className="text-small">{description}</p>
                    </div>
                  }
                  placement="top"
                  showArrow={true}
                >
                  <Icon
                    className="text-default-400 flex-shrink-0 cursor-help"
                    icon="lucide:info"
                  />
                </Tooltip>
              </div>
              <span className="text-small text-default-500 truncate">
                {type === "INFORMATIVE"
                  ? "Campo informativo"
                  : type === "TASK"
                    ? "Campo de tarea"
                    : type === "DOCUMENT"
                      ? "Campo de documento"
                      : type === "TASK_WITH_SUBTASKS"
                        ? "Campo con subtareas"
                        : type === "SELECTION"
                          ? "Campo de selección"
                          : ""}
              </span>
            </div>
            <div className="relative flex justify-end items-center w-full">
              {renderFieldInput()}

              {obs || isObsEditing ? (
                <div className="flex items-center w-1/2 mx-4">
                  <Input
                    className="flex-grow"
                    endContent={
                      isObsEditing ? (
                        <div className="flex items-center gap-2">
                          <Button
                            isIconOnly
                            aria-label="Cancel"
                            isDisabled={!edit}
                            size="sm"
                            variant="light"
                            onPress={() => {
                              setTempObs(obs);
                              setIsObsEditing(false);
                            }}
                          >
                            <Icon
                              className="text-default-500"
                              icon="lucide:x"
                            />
                          </Button>
                          <Button
                            isIconOnly
                            aria-label="Save"
                            color="primary"
                            isDisabled={!edit}
                            size="sm"
                            variant="flat"
                            onPress={toggleObsEditing}
                          >
                            <Icon icon="lucide:check" />
                          </Button>
                        </div>
                      ) : (
                        <Button
                          isIconOnly
                          aria-label="Edit inline"
                          className="flex-shrink-0"
                          color="primary"
                          isDisabled={!edit}
                          size="sm"
                          variant="flat"
                          onPress={toggleObsEditing}
                        >
                          <Icon
                            className="text-default-500"
                            icon="lucide:pencil"
                          />
                        </Button>
                      )
                    }
                    isDisabled={!edit}
                    placeholder="Insertar observación"
                    readOnly={!isObsEditing || !edit}
                    startContent={
                      <Icon
                        className="text-default-400 flex-shrink-0"
                        icon="lucide:message-square"
                      />
                    }
                    value={isObsEditing ? tempObs : obs}
                    onKeyDown={
                      isObsEditing && edit ? handleObsKeyDown : undefined
                    }
                    onValueChange={setTempObs}
                  />
                </div>
              ) : null}
            </div>
            <div className="flex-shrink-0">
              <Button
                isIconOnly
                aria-label="Edit observation in modal"
                className="flex-shrink-0"
                color="primary"
                size="sm"
                variant="flat"
                onPress={handleModalOpen}
              >
                <Icon className="text-lg" icon={"lucide:eye"} />
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>

      {subtasksItems()}

      <Modal isOpen={isModalOpen} size="2xl" onClose={handleModalClose}>
        <ModalContent>
          {() => (
            <Form className="w-full" onSubmit={handleSaveObservation}>
              <ModalHeader className="flex flex-col gap-1">
                <h2 className="text-xl font-semibold flex items-center gap-2">
                  Observación - {title}
                </h2>
              </ModalHeader>
              <ModalBody className="w-full gap-4">
                <Input
                  isRequired
                  className="w-full"
                  isDisabled={!edit}
                  label="Observación"
                  labelPlacement="outside"
                  placeholder="Insertar observación"
                  readOnly={!edit}
                  startContent={
                    <Icon
                      className="text-default-400 pointer-events-none flex-shrink-0"
                      icon="lucide:message-square"
                    />
                  }
                  value={tempObs}
                  onValueChange={setTempObs}
                />
              </ModalBody>
              <ModalFooter className="w-full flex justify-between">
                <Button variant="flat" onPress={handleModalClose}>
                  {edit ? "Cancelar" : "Cerrar"}
                </Button>
                {edit && (
                  <Button color="primary" type="submit">
                    Guardar
                  </Button>
                )}
              </ModalFooter>
            </Form>
          )}
        </ModalContent>
      </Modal>

      {/* Subtask Observation Modal */}
      {subtaskModalOpen && (
        <Modal
          isOpen={!!subtaskModalOpen}
          size="2xl"
          onClose={handleSubtaskModalClose}
        >
          <ModalContent>
            {() => {
              const subtaskId = subtaskModalOpen;
              const currentSubtask = subtasks.find(
                (st) => st.id.toString() === subtaskId,
              );

              if (!currentSubtask) return null;

              return (
                <Form
                  className="w-full"
                  onSubmit={(e) => handleSaveSubtaskObservation(subtaskId, e)}
                >
                  <ModalHeader className="flex flex-col gap-1">
                    <h2 className="text-xl font-semibold flex items-center gap-2">
                      Observación - {currentSubtask.title}
                    </h2>
                  </ModalHeader>
                  <ModalBody className="w-full gap-4">
                    <Input
                      isRequired
                      className="w-full"
                      isDisabled={!edit}
                      label="Observación"
                      labelPlacement="outside"
                      placeholder="Insertar observación"
                      readOnly={!edit}
                      startContent={
                        <Icon
                          className="text-default-400 pointer-events-none flex-shrink-0"
                          icon="lucide:message-square"
                        />
                      }
                      value={tempSubtaskObs[subtaskId] || ""}
                      onValueChange={(val) =>
                        setTempSubtaskObs((prev) => ({
                          ...prev,
                          [subtaskId]: val,
                        }))
                      }
                    />
                  </ModalBody>
                  <ModalFooter className="w-full flex justify-between">
                    <Button variant="flat" onPress={handleSubtaskModalClose}>
                      {edit ? "Cancelar" : "Cerrar"}
                    </Button>
                    {edit && (
                      <Button color="primary" type="submit">
                        Guardar
                      </Button>
                    )}
                  </ModalFooter>
                </Form>
              );
            }}
          </ModalContent>
        </Modal>
      )}
    </>
  );
});

export default Field;
